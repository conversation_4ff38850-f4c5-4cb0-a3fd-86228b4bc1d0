# Quantity Discount Implementation Setup

## Overview
This implementation adds real discount functionality to the quantity-based savings buttons on product pages. Only customers who use the specific "Add 2 to cart" and "Add 3 to cart" buttons will receive the discounts.

## Required Shopify Admin Setup

### 1. Create Discount Codes

You need to create these discount codes in your Shopify Admin:

#### QUANTITY_SAVE_5 (5% discount for 2+ items)
- **Discount Type**: Percentage
- **Value**: 5%
- **Minimum Requirements**: None (controlled by code)
- **Customer Eligibility**: Everyone
- **Usage Limits**: No limits
- **Active Dates**: No end date

#### QUANTITY_SAVE_10 (10% discount for 3+ items)  
- **Discount Type**: Percentage
- **Value**: 10%
- **Minimum Requirements**: None (controlled by code)
- **Customer Eligibility**: Everyone
- **Usage Limits**: No limits
- **Active Dates**: No end date

### 2. How to Create Discount Codes

1. Go to **Shopify Admin > Discounts**
2. Click **Create discount**
3. Choose **Discount code**
4. Enter the code name (e.g., `QUANTITY_SAVE_5`)
5. Set **Discount type** to **Percentage**
6. Enter the **Value** (5 or 10)
7. Leave **Minimum requirements** empty
8. Set **Customer eligibility** to **Everyone**
9. Leave **Usage limits** unchecked
10. Set **Active dates** with no end date
11. Click **Save**

## How It Works

### Technical Implementation

1. **Cart Line Attributes**: When customers use the quantity buttons, items are added to cart with special attributes:
   - `_quantity_discount`: The discount percentage (5 or 10)
   - `_discount_eligible`: Marks the item as eligible for quantity discount
   - `_discount_session`: Unique session ID to prevent conflicts

2. **Automatic Discount Application**: After items are added to cart, the system automatically applies the appropriate discount code (`QUANTITY_SAVE_5` or `QUANTITY_SAVE_10`)

3. **Selective Application**: Only items added via the specific quantity buttons receive discounts. Regular cart additions remain at full price.

### User Experience

- **2-Item Button**: Shows "SAVE 5%" badge, applies 5% discount automatically
- **3-Item Button**: Shows "SAVE 10%" badge, applies 10% discount automatically  
- **Regular Add to Cart**: No discount applied
- **Price Display**: Buttons show the discounted price per bag and total savings

### Benefits

✅ **Precise Control**: Only specific button clicks trigger discounts
✅ **No Cart Conflicts**: Regular additions won't get unintended discounts
✅ **Real Savings**: Customers see actual price reductions in cart
✅ **Trackable**: You can see which orders used quantity discounts
✅ **Flexible**: Easy to modify discount percentages or add new tiers

## Testing

1. **Create the discount codes** in Shopify Admin as described above
2. **Test the buttons** on a product page:
   - Use "Add 2 to cart" button - should apply 5% discount
   - Use "Add 3 to cart" button - should apply 10% discount
   - Use regular "Add 1 to cart" button - should NOT apply discount
3. **Verify in cart** that discounts are applied correctly
4. **Check order details** to confirm discount codes were used

## Troubleshooting

### If discounts aren't applying:
1. Verify discount codes exist in Shopify Admin with exact names
2. Check that codes are active and have no usage limits
3. Ensure codes have no minimum purchase requirements
4. Clear browser cache and test again

### If wrong discounts apply:
1. Check that discount code names match exactly (`QUANTITY_SAVE_5`, `QUANTITY_SAVE_10`)
2. Verify the discount percentages in Shopify Admin match the button labels

## Future Enhancements

- Add more quantity tiers (4+ items, 5+ items, etc.)
- Create product-specific discount codes
- Add time-limited quantity promotions
- Implement customer-specific quantity discounts
