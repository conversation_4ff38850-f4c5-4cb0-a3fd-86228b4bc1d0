import {Link} from 'react-router';
import {Money, Image} from '@shopify/hydrogen';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductFragment} from 'storefrontapi.generated';

interface CrossSellProductsProps {
  currentProduct: ProductFragment;
  allProducts?: any[];
}

export function CrossSellProducts({currentProduct, allProducts = []}: CrossSellProductsProps) {
  const {open} = useAside();

  // Hidden product IDs that should not appear in cross-sell
  const hiddenProductIds = [
    'gid://shopify/Product/8469994373435', // Gold Rush Blend - Dark Roast
    'gid://shopify/Product/8469985231163', // Silver City Blend - Medium Roast
    'gid://shopify/Product/8296601387323', // Big River Blend - Medium Roast
  ];

  // Ensure we have valid products array and filter out hidden products
  const validProducts = Array.isArray(allProducts) ? allProducts.filter(product =>
    product &&
    product.id &&
    product.title &&
    product.handle &&
    !hiddenProductIds.includes(product.id) // Filter out hidden products
  ) : [];

  // Get K-cup products first
  const kcupProducts = validProducts.filter(product =>
    product.title.toLowerCase().includes('k-cup') ||
    product.title.toLowerCase().includes('kcup') ||
    product.tags?.some((tag: string) => tag.toLowerCase().includes('k-cup'))
  ).slice(0, 1); // Get 1 K-cup product

  // Get 3 other products (excluding current product and K-cups)
  const otherProducts = validProducts.filter(product =>
    product.id !== currentProduct.id &&
    !product.title.toLowerCase().includes('k-cup') &&
    !product.title.toLowerCase().includes('kcup') &&
    !product.title.toLowerCase().includes('box') &&
    !product.title.toLowerCase().includes('bundle') &&
    product.availableForSale !== false // Include products where availableForSale is true or undefined
  ).slice(0, 3);

  // Combine K-cups and other products for total of 4
  const crossSellProducts = [...kcupProducts, ...otherProducts].slice(0, 4);

  // Fallback products if no dynamic products are loaded
  const fallbackProducts = [
    {
      id: 'kcup-fallback',
      title: 'K-Cup Variety Pack',
      handle: 'collections/all',
      price: '$24.99',
      description: 'Convenient single-serve pods',
      isKcup: true,
      availableForSale: true,
      featuredImage: null,
      priceRange: null,
      variants: { nodes: [] }
    },
    {
      id: 'coffee-fallback-1',
      title: 'Mountain Blend Coffee',
      handle: 'collections/all',
      price: '$18.99',
      description: 'Rich and smooth blend',
      isKcup: false,
      availableForSale: true,
      featuredImage: null,
      priceRange: null,
      variants: { nodes: [] }
    },
    {
      id: 'coffee-fallback-2',
      title: 'Dark Roast Coffee',
      handle: 'collections/all',
      price: '$19.99',
      description: 'Bold and intense flavor',
      isKcup: false,
      availableForSale: true,
      featuredImage: null,
      priceRange: null,
      variants: { nodes: [] }
    },
    {
      id: 'coffee-fallback-3',
      title: 'Light Roast Coffee',
      handle: 'collections/all',
      price: '$17.99',
      description: 'Bright and crisp taste',
      isKcup: false,
      availableForSale: true,
      featuredImage: null,
      priceRange: null,
      variants: { nodes: [] }
    }
  ];

  // Ensure we always have 4 products to show
  let productsToShow = crossSellProducts.length > 0 ? crossSellProducts : [];

  // If we don't have enough real products, fill with fallbacks
  if (productsToShow.length < 4) {
    const needed = 4 - productsToShow.length;
    productsToShow = [...productsToShow, ...fallbackProducts.slice(0, needed)];
  }

  // If no products to show, display a message
  if (productsToShow.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-[#3a5c5c] text-lg">No related products available at the moment.</p>
        <Link
          to="/collections/all"
          className="inline-block mt-4 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-2 px-6 rounded-lg transition-colors"
        >
          Browse All Products
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 animate-fade-in-up">
      {productsToShow.map((product, index) => {
        const isKcup = product.title?.toLowerCase().includes('k-cup') ||
                      product.title?.toLowerCase().includes('kcup') ||
                      product.isKcup;

        // Stagger animation delay for each card
        const animationDelay = `${index * 150}ms`;
        
        return (
          <Link
            key={product.id || `fallback-${index}`}
            to={product.handle ? `/products/${product.handle}` : '/collections/all'}
            className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out transform hover:-translate-y-3 hover:scale-[1.05] border border-gray-100 hover:border-gray-200 animate-slide-in-up cursor-pointer block"
            style={{ animationDelay }}
          >
            {/* Product Badge */}
            <div className="flex justify-between items-start mb-4">
              <div className={`px-3 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 ${
                isKcup
                  ? 'bg-[#5d8e8e] text-white group-hover:bg-[#4a7373]'
                  : 'bg-[#db8027] text-white group-hover:bg-[#c4721f]'
              }`}>
                {isKcup ? '🚀 Quick Brew' : '☕ Premium'}
              </div>
              {index === 0 && (
                <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 group-hover:bg-green-400">
                  Popular
                </div>
              )}
            </div>

            {/* Product Image */}
            {product.featuredImage?.url ? (
              <div className="aspect-square rounded-xl overflow-hidden mb-4 bg-gray-100 transition-transform duration-300 group-hover:scale-105">
                <Image
                  data={product.featuredImage}
                  aspectRatio="1/1"
                  sizes="(max-width: 640px) 50vw, (max-width: 1024px) 25vw, 20vw"
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                />
              </div>
            ) : (
              <div className="aspect-square rounded-xl overflow-hidden mb-4 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center transition-transform duration-300 group-hover:scale-105">
                <span className="text-4xl transition-transform duration-300 group-hover:scale-110">{isKcup ? '☕' : '🌱'}</span>
              </div>
            )}

            {/* Product Info */}
            <div className="space-y-3">
              <h3 className="font-bold text-[#3a5c5c] text-lg line-clamp-2 min-h-[3.5rem]">
                {product.title}
              </h3>
              
              <p className="text-gray-600 text-sm line-clamp-2">
                {product.description || (isKcup ? 'Convenient single-serve pods for quick brewing' : 'Premium coffee beans roasted to perfection')}
              </p>

              {/* Price */}
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-[#db8027]">
                  {product.priceRange?.minVariantPrice?.amount ? (
                    <Money data={product.priceRange.minVariantPrice} />
                  ) : (
                    <span>{product.price || '$19.99'}</span>
                  )}
                </div>
                {isKcup && (
                  <div className="text-xs text-gray-500">per pack</div>
                )}
              </div>

              {/* Special Features */}
              <div className="space-y-2">
                {isKcup ? (
                  <div className="flex items-center space-x-2 text-sm text-[#5d8e8e]">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 9.293 10.793a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                    </svg>
                    <span>Ready in 60 seconds</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-sm text-[#db8027]">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Premium quality</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 pt-2" onClick={(e) => e.stopPropagation()}>
                <button
                  className="flex-1 bg-[#3a5c5c] hover:bg-[#2d4747] hover:scale-105 hover:shadow-lg text-white font-medium py-3 px-4 rounded-lg text-center text-sm transition-all duration-300 transform active:scale-95"
                >
                  <span className="text-white">View Details</span>
                </button>
                
                {product.variants?.nodes?.[0]?.id ? (
                  <AddToCartButton
                    disabled={!product.availableForSale || !product.variants.nodes[0].availableForSale}
                    onClick={() => open('cart')}
                    lines={[
                      {
                        merchandiseId: product.variants.nodes[0].id,
                        quantity: 1,
                        selectedVariant: product.variants.nodes[0],
                      },
                    ]}
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-3 px-4 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
                  >
                    Add to Cart
                  </AddToCartButton>
                ) : (
                  <button
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-3 px-4 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 text-center"
                  >
                    Shop Now
                  </button>
                )}
              </div>
            </div>
          </Link>
        );
      })}
    </div>
  );
}
